import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim

# 生成数据
x = np.linspace(-2*np.pi , 2*np.pi , 1000)
y = np.sin(x)

# 转换为PyTorch张量

x_train = torch.tensor(x, dtype=torch.float32).view(-1, 1)
y_train = torch.tensor(y, dtype=torch.float32).view(-1, 1)

# 创建神经网络模型
class SimpleNN(nn.Module):
    def __init__(self):
        super(SimpleNN, self).__init__()
        self.fc1 = nn.Linear(1, 64)
        self.fc2 = nn.Linear(64, 64)
        self.fc3 = nn.Linear(64, 1)

    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        x = self.fc3(x)
        return x

# 创建模型实例
model = SimpleNN()

# 定义损失函数和优化器
criterion = nn.MSELoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)
epochs = 1000
for epoch in range(epochs):
    model.train()
    optimizer.zero_grad() # 每次迭代开始时清零梯度

    # 前向传播
    y_pred = model(x_train)

    # 计算损失
    loss = criterion(y_pred, y_train)

    # 反向传播和优化
    loss.backward()
    optimizer.step()

    if (epoch + 1) % 100 == 0:
        print(f'Epoch [{epoch + 1}/{epochs}], Loss: {loss.item():.4f}')

model.eval()
optimizer.zero_grad()
#with torch.no_grad():
y_pred = model(x_train)

# 绘制结果
plt.figure(figsize=(10, 6))
plt.plot(x, y, label='真实值 (sin)', color='blue')
plt.plot(x, y_pred.detach().numpy(), label='预测值 (NN)', color='red')
plt.title('神经网络函数拟合')
plt.xlabel('x')
plt.ylabel('y')
plt.legend()
plt.grid()
plt.show()