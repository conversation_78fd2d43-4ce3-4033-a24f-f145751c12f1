import torch
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt
import numpy as np

# random data
np.random.seed(42)
x = np.random.uniform(1e5, 1e8, 1000).astype(np.float32)
y = 0.016*x**0.3/(1-0.003*x**0.3) + np.random.normal(0, 0.0001, size=x.shape).astype(np.float32)


x_tensor = torch.tensor(x).unsqueeze(1)  # Shape: (1000, 1)
y_tensor = torch.tensor(y).unsqueeze(1)  # Shape: (1000, 1)


class CubicFitNet(nn.Module):
    def __init__(self):
        super(CubicFitNet, self).__init__()
        self.model = nn.Sequential(
            nn.Linear(1, 64),
            nn.ReLU(),
            nn.Linear(64, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )

    def forward(self, x):
        return self.model(x)


model = CubicFitNet()


criterion = nn.MSELoss()
optimizer = optim.Adam(model.parameters(), lr=0.01)


def train_model(model, x_tensor, y_tensor, epochs=1000):
    loss_history = []
    for epoch in range(epochs):
        model.train()
        optimizer.zero_grad()
        y_pred = model(x_tensor)
        loss = criterion(y_pred, y_tensor)
        loss.backward()
        optimizer.step()

        loss_history.append(loss.item())
        if (epoch + 1) % 100 == 0:
            print(f"Epoch [{epoch+1}/{epochs}], Loss: {loss.item():.4f}")
    return loss_history

loss_history = train_model(model, x_tensor, y_tensor)


model.eval()
x_test = torch.linspace(-2, 2, 100).unsqueeze(1)
y_test_pred = model(x_test).detach().numpy()

# Plot results
plt.figure(figsize=(10, 5))
plt.scatter(x, y, color='blue', alpha=0.3, label='Noisy data')
plt.plot(x_test.numpy(), y_test_pred, color='red', label='Model prediction')
plt.plot(x_test.numpy(), 0.016*x_test.numpy()**0.3/(1-0.003*x_test.numpy()**0.3), color='green', linestyle='--', label='True function')
plt.legend()
plt.xlabel('x')
plt.ylabel('y')
plt.title('Fitting $y = x^3$ with NN')
plt.show()

# Plot loss history
plt.figure(figsize=(10, 5))
plt.plot(loss_history, label='Loss')
plt.xlabel('Epoch')
plt.ylabel('MSE Loss')
plt.title('Training Loss History')
plt.legend()
plt.show()
