import requests
from bs4 import BeautifulSoup
import pandas as pd

# 目标网页URL
url = "https://www.seiee.sjtu.edu.cn/via/yjspy_zsgz_sszs/11230.html"

# 发送HTTP请求获取网页内容
response = requests.get(url)
response.encoding = "utf-8"  # 确保编码正确

# 使用BeautifulSoup解析HTML
soup = BeautifulSoup(response.text, "html.parser")

# 查找表格（根据实际HTML结构调整选择器）
table = soup.find("table")  # 或使用更具体的CSS选择器

# 使用pandas读取表格
df = pd.read_html(str(table))[0]  # 将HTML表格转换为DataFrame

# 保存为CSV文件
df.to_csv("extracted_data1.csv", index=True, encoding="utf-8-sig")
print("数据已保存到 extracted_data.csv")