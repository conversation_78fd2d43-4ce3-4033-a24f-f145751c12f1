import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
import torchvision
import torchvision.transforms as transforms
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import math

# 设置设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

# 超参数
batch_size = 128
learning_rate = 1e-3
epochs = 60
timesteps = 1000
beta_start = 1e-4
beta_end = 0.02
img_size = 28


class DiffusionScheduler:
    """扩散过程的噪声调度器"""

    def __init__(self, timesteps=1000, beta_start=1e-4, beta_end=0.02):
        self.timesteps = timesteps

        # 线性调度的beta值
        self.betas = torch.linspace(beta_start, beta_end, timesteps)
        self.alphas = 1 - self.betas
        self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
        self.alphas_cumprod_prev = F.pad(self.alphas_cumprod[:-1], (1, 0), value=1.0)

        # 预计算用于采样的参数
        self.sqrt_alphas_cumprod = torch.sqrt(self.alphas_cumprod)
        self.sqrt_one_minus_alphas_cumprod = torch.sqrt(1 - self.alphas_cumprod)

        # 用于去噪过程
        self.sqrt_recip_alphas = torch.sqrt(1.0 / self.alphas)
        self.posterior_variance = self.betas * (1 - self.alphas_cumprod_prev) / (1 - self.alphas_cumprod)

    def add_noise(self, x_start, t, noise=None):
        """向图像添加噪声 (前向过程)"""
        if noise is None:
            noise = torch.randn_like(x_start)

        sqrt_alphas_cumprod_t = self.sqrt_alphas_cumprod[t].reshape(-1, 1, 1, 1)
        sqrt_one_minus_alphas_cumprod_t = self.sqrt_one_minus_alphas_cumprod[t].reshape(-1, 1, 1, 1)

        return sqrt_alphas_cumprod_t * x_start + sqrt_one_minus_alphas_cumprod_t * noise


class SinusoidalPositionalEmbedding(nn.Module):
    """正弦位置编码用于时间步嵌入"""

    def __init__(self, dim):
        super().__init__()
        self.dim = dim

    def forward(self, time):
        device = time.device
        half_dim = self.dim // 2
        embeddings = math.log(10000) / (half_dim - 1)
        embeddings = torch.exp(torch.arange(half_dim, device=device) * -embeddings)
        embeddings = time[:, None] * embeddings[None, :]
        embeddings = torch.cat((embeddings.sin(), embeddings.cos()), dim=-1)
        return embeddings


class ResBlock(nn.Module):
    """残差块"""

    def __init__(self, in_channels, out_channels, time_emb_dim):
        super().__init__()
        self.time_mlp = nn.Sequential(
            nn.SiLU(),
            nn.Linear(time_emb_dim, out_channels)
        )

        # 确保分组数量能被通道数整除
        def get_group_norm(channels):
            # 选择合适的分组数量
            if channels >= 32:
                return nn.GroupNorm(min(32, channels), channels)
            elif channels >= 16:
                return nn.GroupNorm(min(16, channels), channels)
            elif channels >= 8:
                return nn.GroupNorm(min(8, channels), channels)
            else:
                return nn.GroupNorm(1, channels)

        self.block1 = nn.Sequential(
            get_group_norm(in_channels),
            nn.SiLU(),
            nn.Conv2d(in_channels, out_channels, 3, padding=1)
        )

        self.block2 = nn.Sequential(
            get_group_norm(out_channels),
            nn.SiLU(),
            nn.Conv2d(out_channels, out_channels, 3, padding=1)
        )

        self.res_conv = nn.Conv2d(in_channels, out_channels, 1) if in_channels != out_channels else nn.Identity()

    def forward(self, x, time_emb):
        h = self.block1(x)
        time_emb = self.time_mlp(time_emb)
        h += time_emb[..., None, None]
        h = self.block2(h)
        return h + self.res_conv(x)


class UNet(nn.Module):
    """U-Net架构的去噪网络"""

    def __init__(self, in_channels=1, out_channels=1, time_emb_dim=256):
        super().__init__()

        # 时间嵌入
        self.time_embedding = nn.Sequential(
            SinusoidalPositionalEmbedding(time_emb_dim),
            nn.Linear(time_emb_dim, time_emb_dim),
            nn.ReLU()
        )

        # 下采样路径
        self.down1 = ResBlock(in_channels, 64, time_emb_dim)
        self.down2 = ResBlock(64, 128, time_emb_dim)
        self.down3 = ResBlock(128, 256, time_emb_dim)

        # 瓶颈层
        self.bottleneck = ResBlock(256, 256, time_emb_dim)

        # 上采样路径
        self.up3 = ResBlock(256 + 256, 128, time_emb_dim)
        self.up2 = ResBlock(128 + 128, 64, time_emb_dim)
        self.up1 = ResBlock(64 + 64, 64, time_emb_dim)

        # 最终输出层
        self.final = nn.Conv2d(64, out_channels, 1)

        # 池化和上采样
        self.pool = nn.MaxPool2d(2)
        self.upsample = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=False)

    def _match_size(self, x, target):
        """确保x的空间尺寸与target匹配"""
        if x.shape[-2:] != target.shape[-2:]:
            x = F.interpolate(x, size=target.shape[-2:], mode='bilinear', align_corners=False)
        return x

    def forward(self, x, timestep):
        # 时间嵌入
        t_emb = self.time_embedding(timestep)

        # 下采样路径，保存特征图
        x1 = self.down1(x, t_emb)  # [B, 64, 28, 28]
        x2 = self.down2(self.pool(x1), t_emb)  # [B, 128, 14, 14]
        x3 = self.down3(self.pool(x2), t_emb)  # [B, 256, 7, 7]

        # 瓶颈层
        x = self.bottleneck(self.pool(x3), t_emb)  # [B, 256, 3, 3]

        # 上采样路径，确保尺寸匹配
        x = self.upsample(x)  # 上采样到 [B, 256, 6, 6]
        x = self._match_size(x, x3)  # 调整到与x3相同的尺寸 [B, 256, 7, 7]
        x = self.up3(torch.cat([x, x3], dim=1), t_emb)  # [B, 128, 7, 7]

        x = self.upsample(x)  # 上采样到 [B, 128, 14, 14]
        x = self._match_size(x, x2)  # 确保与x2尺寸匹配
        x = self.up2(torch.cat([x, x2], dim=1), t_emb)  # [B, 64, 14, 14]

        x = self.upsample(x)  # 上采样到 [B, 64, 28, 28]
        x = self._match_size(x, x1)  # 确保与x1尺寸匹配
        x = self.up1(torch.cat([x, x1], dim=1), t_emb)  # [B, 64, 28, 28]

        return self.final(x)


class DiffusionModel:
    """完整的扩散模型类"""

    def __init__(self, timesteps=1000, beta_start=1e-4, beta_end=0.02):
        self.model = UNet().to(device)
        self.scheduler = DiffusionScheduler(timesteps, beta_start, beta_end)
        self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate)

        # 将调度器参数移动到设备
        for attr in ['betas', 'alphas', 'alphas_cumprod', 'alphas_cumprod_prev',
                     'sqrt_alphas_cumprod', 'sqrt_one_minus_alphas_cumprod',
                     'sqrt_recip_alphas', 'posterior_variance']:
            setattr(self.scheduler, attr, getattr(self.scheduler, attr).to(device))

    def train_step(self, x):
        """单步训练"""
        batch_size = x.shape[0]

        # 随机选择时间步
        t = torch.randint(0, self.scheduler.timesteps, (batch_size,), device=device)

        # 生成随机噪声
        noise = torch.randn_like(x)

        # 前向过程：添加噪声
        x_noisy = self.scheduler.add_noise(x, t, noise)

        # 预测噪声
        predicted_noise = self.model(x_noisy, t)

        # 计算损失
        loss = F.mse_loss(predicted_noise, noise)

        # 反向传播
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        return loss.item()

    @torch.no_grad()
    def sample(self, num_samples=16):
        """采样生成图像"""
        self.model.eval()

        # 从纯噪声开始
        x = torch.randn(num_samples, 1, img_size, img_size, device=device)

        # 逐步去噪
        for i in tqdm(reversed(range(self.scheduler.timesteps)), desc="采样中"):
            t = torch.full((num_samples,), i, device=device, dtype=torch.long)

            # 预测噪声
            predicted_noise = self.model(x, t)

            # 计算去噪后的图像
            alpha = self.scheduler.alphas[i]
            alpha_cumprod = self.scheduler.alphas_cumprod[i]
            beta = self.scheduler.betas[i]
            sqrt_one_minus_alphas_cumprod = self.scheduler.sqrt_one_minus_alphas_cumprod[i]
            sqrt_recip_alphas = self.scheduler.sqrt_recip_alphas[i]

            # 去噪
            x = sqrt_recip_alphas * (x - beta * predicted_noise / sqrt_one_minus_alphas_cumprod)

            # 添加噪声（除了最后一步）
            if i > 0:
                posterior_variance = self.scheduler.posterior_variance[i]
                noise = torch.randn_like(x)
                x = x + torch.sqrt(posterior_variance) * noise

        self.model.train()
        return x


def load_data():
    """加载MNIST数据集"""
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.5,), (0.5,))  # 归一化到[-1, 1]
    ])

    dataset = torchvision.datasets.MNIST(
        root='./data', train=True, download=True, transform=transform
    )

    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    return dataloader


def visualize_samples(samples, title="生成的样本", nrow=4):
    """可视化生成的样本"""
    # 将值从[-1, 1]转换到[0, 1]
    samples = (samples + 1) / 2
    samples = torch.clamp(samples, 0, 1)

    grid = torchvision.utils.make_grid(samples, nrow=nrow, padding=2)
    plt.figure(figsize=(10, 10))
    plt.imshow(grid.permute(1, 2, 0).cpu().numpy(), cmap='gray')
    plt.title(title)
    plt.axis('off')
    plt.show()


def main():
    """主训练函数"""
    # 加载数据
    dataloader = load_data()

    # 创建模型
    diffusion = DiffusionModel(timesteps=timesteps, beta_start=beta_start, beta_end=beta_end)

    print("开始训练...")

    # 训练循环
    for epoch in range(epochs):
        total_loss = 0
        num_batches = 0

        progress_bar = tqdm(dataloader, desc=f"Epoch {epoch + 1}/{epochs}")
        for batch_idx, (x, _) in enumerate(progress_bar):
            x = x.to(device)

            # 训练步骤
            loss = diffusion.train_step(x)
            total_loss += loss
            num_batches += 1

            # 更新进度条
            progress_bar.set_postfix({'Loss': f'{loss:.4f}'})

        avg_loss = total_loss / num_batches
        print(f"Epoch {epoch + 1}, 平均损失: {avg_loss:.4f}")

        # 每10个epoch生成样本
        if (epoch + 1) % 10 == 0:
            print("生成样本...")
            samples = diffusion.sample(num_samples=16)
            visualize_samples(samples, f"Epoch {epoch + 1} 生成的样本")

    # 训练完成后生成最终样本
    print("训练完成！生成最终样本...")
    final_samples = diffusion.sample(num_samples=25)
    visualize_samples(final_samples, "最终生成的样本", nrow=5)

    # 保存模型
    torch.save(diffusion.model.state_dict(), 'mnist_diffusion_model.pth')
    print("模型已保存为 mnist_diffusion_model.pth")


if __name__ == "__main__":
    main()